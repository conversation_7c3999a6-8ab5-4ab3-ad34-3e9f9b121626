import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { type Actions, error, fail } from '@sveltejs/kit';
import { upsertBudgetLineItem } from '$lib/project_utils';
import type { PageServerLoad } from './$types';
import { budgetItemSchema } from '$lib/schemas/project';
import { buildBudgetTree, type EnhancedWbsItemTree } from '$lib/budget_utils';

export const load: PageServerLoad = async ({ params, locals, cookies, depends }) => {
	depends('project:budget');

	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	console.log({ projectData });

	// 1) Fetch *all* WBS items + their current budget items in one go
	const { data: rawNodes, error: wbsError } = await locals.supabase
		.from('wbs_library_item')
		.select(
			`*,
      budget_line_item_current(*)
    `,
		)
		.eq('wbs_library_id', projectData.wbs_library_id)
		.eq('budget_line_item_current.project_id', projectData.project_id)
		.or(
			`client_id.eq.${projectData.client.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
		);

	if (wbsError) {
		console.error('Error fetching WBS items:', wbsError);
		throw error(500, { message: 'Error loading budget data' });
	}

	const wbsItems = rawNodes.map((i) => ({
		label: `${i.code}: ${i.description}`,
		value: i.wbs_library_item_id,
	}));

	const wbsTree: EnhancedWbsItemTree[] = buildBudgetTree(rawNodes || []);

	// Create the form with the budget item schema
	const form = await superValidate(zod(budgetItemSchema));

	return {
		client: projectData.client,
		project: projectData,
		wbsItems,
		wbsTree,
		canEditProject: canEditProject || false,
		form,
	};
};

export const actions: Actions = {
	// Upsert a budget line item
	upsertBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		// Validate the form
		const form = await superValidate(request, zod(budgetItemSchema));
		console.log({ form });

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Update the budget line item
		try {
			const id = await upsertBudgetLineItem(supabase, form.data);
			console.log({ id });
			return message(form, { type: 'success', text: 'Budget line item saved successfully' });
		} catch (error) {
			console.error('Error upserting budget line item:', error);
			return message(
				form,
				{ type: 'error', text: 'Error saving budget line item' },
				{ status: 500 },
			);
		}
	},

	// Delete a budget line item
	deleteBudgetItem: async ({ request, locals }) => {
		const { supabase } = locals;

		const form = await superValidate(request, zod(budgetItemSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { project_id: projectId, budget_line_item_id: budgetLineItemId } = form.data;

		if (!projectId || !budgetLineItemId) {
			return fail(400, { success: false, message: 'Missing required fields' });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: projectId,
		});

		if (!canEdit) {
			return fail(403, {
				success: false,
				message: 'You do not have permission to edit this project',
			});
		}

		// Delete the specific budget line item by its ID
		try {
			const { error } = await supabase
				.from('budget_line_item_current')
				.delete()
				.eq('budget_line_item_id', budgetLineItemId);

			if (error) throw error;

			return {
				success: true,
				message: { type: 'success', text: 'Budget line item deleted successfully' },
			};
		} catch (error) {
			console.error('Error deleting budget line item:', error);
			return fail(500, { success: false, message: 'Error deleting budget line item' });
		}
	},
};
